import 'package:bruno/bruno.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_getx_widget.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';
import 'package:provider/provider.dart';
import 'package:sp_util/sp_util.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/bean/camera_config.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';
import 'package:xiongmao_clean_flutter_module/util/image_size_utils.dart';
import 'package:xiongmao_clean_flutter_module/res/constant.dart';

import '../../project/bean/project_manager_entity.dart';
import '../bean/role_custom_data.dart';
import '../bean/work_post_manager_entity.dart';
import '../bean/work_rules_config_entity.dart';

class WorkRulesCameraLogoController extends GetxController {
  ///常规内容 水印LOGO图片位置 1左上 2左下 3居中 4右上 有logo则必传
  List<BrnCommonActionSheetItem> actions = [
    BrnCommonActionSheetItem('左上'),
    BrnCommonActionSheetItem('左下'),
    BrnCommonActionSheetItem('居中'),
    BrnCommonActionSheetItem('右上'),
  ];

  ///记录位置 默认是左下（这个是位置的信息，传递給后段，根据这个来计算）
  var positionPhoto = 2.obs;

  ///选择的照片，需要上传到七牛
  var localPath = ''.obs;

  ///图片的高
  var logoWidth = 0.obs;

  ///图片的宽
  var logoHeight = 0.obs;

  ///可调整的图片大小（这个是计算的基准，）
  var scalePhoto = 50.obs;

  ///再这里进行数据填充
  void fillData(CameraConfig config) {
    localPath.value = config.logoPath;
    logoWidth.value = config.logoWidth;
    logoHeight.value = config.logoHeight;
    scalePhoto.value = config.scale < 30 ? 30 : config.scale;
    positionPhoto.value = config.logoPosition;

    // 如果是网络图片且宽高为0，则自动获取图片尺寸
    if (_isNetworkImage(config.logoPath) &&
        (config.logoWidth == 0 || config.logoHeight == 0)) {
      _getNetworkImageSize(config.logoPath);
    }
  }

  /// 判断是否为网络图片
  bool _isNetworkImage(String imagePath) {
    return imagePath.startsWith('http://') || imagePath.startsWith('https://');
  }

  /// 获取网络图片尺寸
  Future<void> _getNetworkImageSize(String imageUrl) async {
    try {
      // 准备请求头，包含 accessToken
      Map<String, String> headers = {
        'accessToken': SpUtil.getString(Constant.accessToken) ?? '',
      };

      Size? imageSize = await ImageSizeUtils.getNetworkImageSize(imageUrl, headers: headers);
      if (imageSize != null) {
        logoWidth.value = imageSize.width.toInt();
        logoHeight.value = imageSize.height.toInt();
        print('网络图片尺寸获取成功: 宽度=${imageSize.width}, 高度=${imageSize.height}');
      } else {
        print('获取网络图片尺寸失败: $imageUrl');
      }
    } catch (e) {
      print('获取网络图片尺寸异常: $e');
    }
  }
}
