import 'package:flutter/material.dart';
import 'package:flustars/flustars.dart';
import 'package:xiongmao_clean_flutter_module/res/constant.dart';
import 'image_size_utils.dart';

/// 图片尺寸获取使用示例
class ImageSizeExample {
  
  /// 示例1: 获取本地图片尺寸
  static Future<void> getLocalImageSizeExample() async {
    String localImagePath = '/storage/emulated/0/DCIM/Camera/IMG_20231201_123456.jpg';
    
    Size? size = await ImageSizeUtils.getLocalImageSize(localImagePath);
    if (size != null) {
      print('本地图片尺寸: 宽度=${size.width.toInt()}, 高度=${size.height.toInt()}');
    } else {
      print('获取本地图片尺寸失败');
    }
  }
  
  /// 示例2: 获取网络图片尺寸（带请求头）
  static Future<void> getNetworkImageSizeExample() async {
    String networkImageUrl = 'https://example.com/image.jpg';
    
    // 如果需要 accessToken，可以这样传递
    Map<String, String> headers = {
      'accessToken': SpUtil.getString(Constant.accessToken) ?? '',
    };
    
    Size? size = await ImageSizeUtils.getNetworkImageSize(networkImageUrl, headers: headers);
    if (size != null) {
      print('网络图片尺寸: 宽度=${size.width.toInt()}, 高度=${size.height.toInt()}');
    } else {
      print('获取网络图片尺寸失败');
    }
  }
  
  /// 示例3: 使用便捷方法自动判断图片类型
  static Future<void> getImageSizeAutoExample() async {
    // 本地图片
    String localPath = '/storage/emulated/0/DCIM/Camera/IMG_20231201_123456.jpg';
    Size? localSize = await ImageSizeUtils.getImageSize(localPath);
    
    // 网络图片
    String networkUrl = 'https://example.com/image.jpg';
    Map<String, String> headers = {
      'accessToken': SpUtil.getString(Constant.accessToken) ?? '',
    };
    Size? networkSize = await ImageSizeUtils.getImageSize(networkUrl, headers: headers);
    
    print('本地图片尺寸: ${localSize?.width.toInt()} x ${localSize?.height.toInt()}');
    print('网络图片尺寸: ${networkSize?.width.toInt()} x ${networkSize?.height.toInt()}');
  }
  
  /// 示例4: 在 image_picker 选择图片后获取尺寸
  static Future<Map<String, dynamic>?> getImageWithSize(String imagePath) async {
    Size? size = await ImageSizeUtils.getLocalImageSize(imagePath);
    
    if (size != null) {
      return {
        'path': imagePath,
        'width': size.width.toInt(),
        'height': size.height.toInt(),
      };
    }
    
    return null;
  }
  
  /// 示例5: 批量获取多张图片的尺寸
  static Future<List<Map<String, dynamic>>> getBatchImageSizes(List<String> imagePaths) async {
    List<Map<String, dynamic>> results = [];
    
    for (String path in imagePaths) {
      Size? size = await ImageSizeUtils.getImageSize(path);
      
      results.add({
        'path': path,
        'width': size?.width.toInt() ?? 0,
        'height': size?.height.toInt() ?? 0,
        'success': size != null,
      });
    }
    
    return results;
  }
}
