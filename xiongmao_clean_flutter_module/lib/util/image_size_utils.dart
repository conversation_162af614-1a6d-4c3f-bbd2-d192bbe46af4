import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:dio/dio.dart';

/// 图片尺寸获取工具类
class ImageSizeUtils {
  
  /// 获取本地图片的尺寸
  /// [imagePath] 本地图片路径
  /// 返回 Size 对象，包含宽度和高度
  static Future<Size?> getLocalImageSize(String imagePath) async {
    try {
      final File imageFile = File(imagePath);
      if (!await imageFile.exists()) {
        print('图片文件不存在: $imagePath');
        return null;
      }

      final Uint8List bytes = await imageFile.readAsBytes();
      return await _getImageSizeFromBytes(bytes);
    } catch (e) {
      print('获取本地图片尺寸失败: $e');
      return null;
    }
  }

  /// 获取网络图片的尺寸
  /// [imageUrl] 网络图片URL
  /// [headers] 可选的请求头，比如 accessToken
  /// 返回 Size 对象，包含宽度和高度
  static Future<Size?> getNetworkImageSize(String imageUrl, {Map<String, String>? headers}) async {
    try {
      final Dio dio = Dio();
      
      // 设置请求选项
      final Options options = Options(
        responseType: ResponseType.bytes,
        headers: headers,
      );

      final Response<Uint8List> response = await dio.get(imageUrl, options: options);
      
      if (response.statusCode == 200 && response.data != null) {
        return await _getImageSizeFromBytes(response.data!);
      } else {
        print('网络图片请求失败: ${response.statusCode}');
        return null;
      }
    } catch (e) {
      print('获取网络图片尺寸失败: $e');
      return null;
    }
  }

  /// 从字节数据获取图片尺寸
  /// [bytes] 图片的字节数据
  /// 返回 Size 对象，包含宽度和高度
  static Future<Size?> _getImageSizeFromBytes(Uint8List bytes) async {
    try {
      final ui.Codec codec = await ui.instantiateImageCodec(bytes);
      final ui.FrameInfo frameInfo = await codec.getNextFrame();
      final ui.Image image = frameInfo.image;
      
      final Size size = Size(
        image.width.toDouble(),
        image.height.toDouble(),
      );
      
      // 释放图片资源
      image.dispose();
      codec.dispose();
      
      return size;
    } catch (e) {
      print('从字节数据获取图片尺寸失败: $e');
      return null;
    }
  }

  /// 获取 ImageProvider 的图片尺寸
  /// [imageProvider] 图片提供者
  /// 返回 Size 对象，包含宽度和高度
  static Future<Size?> getImageProviderSize(ImageProvider imageProvider) async {
    try {
      final Completer<Size?> completer = Completer<Size?>();
      final ImageStream stream = imageProvider.resolve(const ImageConfiguration());
      
      late ImageStreamListener listener;
      listener = ImageStreamListener(
        (ImageInfo info, bool synchronousCall) {
          final Size size = Size(
            info.image.width.toDouble(),
            info.image.height.toDouble(),
          );
          stream.removeListener(listener);
          completer.complete(size);
        },
        onError: (dynamic exception, StackTrace? stackTrace) {
          stream.removeListener(listener);
          completer.complete(null);
        },
      );
      
      stream.addListener(listener);
      return await completer.future;
    } catch (e) {
      print('获取 ImageProvider 图片尺寸失败: $e');
      return null;
    }
  }

  /// 获取图片尺寸的便捷方法
  /// 自动判断是本地图片还是网络图片
  /// [imagePath] 图片路径或URL
  /// [headers] 网络图片请求头（可选）
  /// 返回 Size 对象，包含宽度和高度
  static Future<Size?> getImageSize(String imagePath, {Map<String, String>? headers}) async {
    if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
      // 网络图片
      return await getNetworkImageSize(imagePath, headers: headers);
    } else {
      // 本地图片
      return await getLocalImageSize(imagePath);
    }
  }
}
